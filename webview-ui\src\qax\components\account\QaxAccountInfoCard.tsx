import { EmptyRequest } from "@shared/proto/cline/common"
import { VSCodeButton } from "@vscode/webview-ui-toolkit/react"
import { useClineAuth } from "@/context/ClineAuthContext"
import { useExtensionState } from "@/context/ExtensionStateContext"
import { QaxAccountServiceClient } from "@/services/grpc-client"

export const QaxAccountInfoCard = () => {
	const { navigateToAccount } = useExtensionState()
	const { qaxUser } = useClineAuth()

	const user = qaxUser

	const handleLogin = () => {
		// 直接调用 QAX 登录服务
		QaxAccountServiceClient.qaxLoginClicked(EmptyRequest.create()).catch((err: any) =>
			console.error("Failed to sign in:", err),
		)
	}

	const handleShowAccount = () => {
		navigateToAccount()
	}

	return (
		<div className="max-w-[600px]">
			{user ? (
				<VSCodeButton appearance="secondary" onClick={handleShowAccount}>
					查看用户信息
				</VSCodeButton>
			) : (
				<div>
					<VSCodeButton className="mt-0" onClick={handleLogin}>
						登录Qax账户
					</VSCodeButton>
				</div>
			)}
		</div>
	)
}
