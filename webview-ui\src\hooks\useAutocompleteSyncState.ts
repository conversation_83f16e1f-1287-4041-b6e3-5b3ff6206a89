import { SyncAutocompleteStateRequest } from "@shared/proto/cline/autocomplete"
import { useEffect, useRef } from "react"
import { UiServiceClient } from "../services/grpc-client"

/**
 * Hook to sync send button state with autocomplete manager
 * This ensures autocomplete is disabled when send button is disabled
 */
export function useAutocompleteSyncState(sendingDisabled: boolean) {
	const previousStateRef = useRef<boolean>(sendingDisabled)

	useEffect(() => {
		// Only send update if state actually changed
		if (previousStateRef.current !== sendingDisabled) {
			previousStateRef.current = sendingDisabled

			// Send state update to backend via gRPC
			UiServiceClient.syncAutocompleteState(
				SyncAutocompleteStateRequest.create({
					sendingDisabled,
					timestamp: Date.now(),
				}),
			).catch((error) => {
				console.error("Failed to sync autocomplete state:", error)
			})

			console.log(`🔄 Autocomplete state sync: sendingDisabled=${sendingDisabled}`)
		}
	}, [sendingDisabled])
}
