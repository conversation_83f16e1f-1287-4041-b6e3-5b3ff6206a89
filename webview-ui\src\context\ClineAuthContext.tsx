import type { UserOrganization } from "@shared/proto/cline/account"
import { EmptyRequest } from "@shared/proto/cline/common"
import { QaxUserInfo } from "@shared/QaxUserInfo"
import deepEqual from "fast-deep-equal"
import type React from "react"
import { createContext, useCallback, useContext, useEffect, useMemo, useState } from "react"
import { AccountServiceClient, QaxAccountServiceClient } from "@/services/grpc-client"

// Define User type (you may need to adjust this based on your actual User type)
export interface ClineUser {
	uid: string
	email?: string
	displayName?: string
	photoUrl?: string
	appBaseUrl?: string
}

// QAX User type
export type QaxUser = QaxUserInfo

export interface ClineAuthContextType {
	clineUser: ClineUser | null
	organizations: UserOrganization[] | null
	activeOrganization: UserOrganization | null
	// QAX 扩展
	qaxUser: QaxUser | null
}

const ClineAuthContext = createContext<ClineAuthContextType | undefined>(undefined)

export const ClineAuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
	const [user, setUser] = useState<ClineUser | null>(null)
	const [userOrganizations, setUserOrganizations] = useState<UserOrganization[] | null>(null)
	// QAX 状态管理 (参考 cline 模式，不依赖 ExtensionState)
	const [qaxUser, setQaxUser] = useState<QaxUser | null>(null)

	const getUserOrganizations = useCallback(async () => {
		try {
			const response = await AccountServiceClient.getUserOrganizations(EmptyRequest.create())
			if (!deepEqual(response.organizations, userOrganizations)) {
				setUserOrganizations(response.organizations)
			}
		} catch (error) {
			console.error("Failed to fetch user organizations:", error)
		}
	}, [])

	const activeOrganization = useMemo(() => {
		return userOrganizations?.find((org) => org.active) ?? null
	}, [userOrganizations])

	useEffect(() => {
		console.log("Extension: ClineAuthContext: user updated:", user?.uid)
	}, [user?.uid])

	useEffect(() => {
		console.log("Extension: ClineAuthContext: qaxUser updated:", qaxUser?.sub)
	}, [qaxUser?.sub])

	// Handle auth status update events
	useEffect(() => {
		const cancelSubscription = AccountServiceClient.subscribeToAuthStatusUpdate(EmptyRequest.create(), {
			onResponse: async (response: any) => {
				if (!response?.user?.uid) {
					setUser(null)
				}
				if (response?.user && user?.uid !== response.user.uid) {
					setUser(response.user)
					// Once we have a new user, fetch organizations that
					// allow us to display the active account in account view UI
					// and fetch the correct credit balance to display on mount
					await getUserOrganizations()
				}
			},
			onError: (error: Error) => {
				console.error("Error in auth callback subscription:", error)
			},
			onComplete: () => {
				console.log("Auth callback subscription completed")
			},
		})

		// Cleanup function to cancel subscription when component unmounts
		return () => {
			cancelSubscription()
		}
	}, [])

	// Handle QAX auth status update events (完全复制 cline 模式)
	useEffect(() => {
		const cancelQaxSubscription = QaxAccountServiceClient.subscribeToQaxAuthStatusUpdate(EmptyRequest.create(), {
			onResponse: async (response: any) => {
				if (!response?.user?.sub) {
					setQaxUser(null)
				}
				if (response?.user && qaxUser?.sub !== response.user.sub) {
					setQaxUser(response.user)
				}
			},
			onError: (error: Error) => {
				console.error("Error in QAX auth callback subscription:", error)
			},
			onComplete: () => {
				console.log("QAX auth callback subscription completed")
			},
		})

		// Cleanup function to cancel subscription when component unmounts
		return () => {
			cancelQaxSubscription()
		}
	}, []) // Remove dependency on qaxUser?.sub to avoid unnecessary re-subscriptions

	return (
		<ClineAuthContext.Provider
			value={{
				clineUser: user,
				organizations: userOrganizations,
				activeOrganization,
				qaxUser,
			}}>
			{children}
		</ClineAuthContext.Provider>
	)
}

export const useClineAuth = () => {
	const context = useContext(ClineAuthContext)
	if (context === undefined) {
		throw new Error("useClineAuth must be used within a ClineAuthProvider")
	}
	return context
}

export const handleSignIn = async () => {
	try {
		AccountServiceClient.accountLoginClicked(EmptyRequest.create()).catch((err) =>
			console.error("Failed to get login URL:", err),
		)
	} catch (error) {
		console.error("Error signing in:", error)
		throw error
	}
}

export const handleSignOut = async () => {
	try {
		await AccountServiceClient.accountLogoutClicked(EmptyRequest.create()).catch((err) =>
			console.error("Failed to logout:", err),
		)
	} catch (error) {
		console.error("Error signing out:", error)
		throw error
	}
}

// QAX 认证函数 (参考 cline 模式)
export const handleQaxSignIn = async () => {
	try {
		QaxAccountServiceClient.qaxLoginClicked(EmptyRequest.create()).catch((err) =>
			console.error("Failed to get QAX login URL:", err),
		)
	} catch (error) {
		console.error("Error signing in to QAX:", error)
		throw error
	}
}

export const handleQaxSignOut = async () => {
	try {
		await QaxAccountServiceClient.qaxLogoutClicked(EmptyRequest.create()).catch((err) =>
			console.error("Failed to logout from QAX:", err),
		)
	} catch (error) {
		console.error("Error signing out from QAX:", error)
		throw error
	}
}
