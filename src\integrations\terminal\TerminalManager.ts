import { arePathsEqual } from "@utils/path"
import { getShellForProfile } from "@utils/shell"
import pWaitFor from "p-wait-for"
import * as vscode from "vscode"
import { mergePromise, TerminalProcess, TerminalProcessResultPromise } from "./TerminalProcess"
import { TerminalInfo, TerminalRegistry } from "./TerminalRegistry"

/*
TerminalManager:
- Creates/reuses terminals
- Runs commands via runCommand(), returning a TerminalProcess
- Handles shell integration events

TerminalProcess extends EventEmitter and implements Promise:
- Emits 'line' events with output while promise is pending
- process.continue() resolves promise and stops event emission
- Allows real-time output handling or background execution

getUnretrievedOutput() fetches latest output for ongoing commands

Enables flexible command execution:
- Await for completion
- Listen to real-time events
- Continue execution in background
- Retrieve missed output later

Notes:
- it turns out some shellIntegration APIs are available on cursor, although not on older versions of vscode
- "By default, the shell integration script should automatically activate on supported shells launched from VS Code."
Supported shells:
Linux/macOS: bash, fish, pwsh, zsh
Windows: pwsh


Example:

const terminalManager = new TerminalManager(context);

// Run a command
const process = terminalManager.runCommand('npm install', '/path/to/project');

process.on('line', (line) => {
	console.log(line);
});

// To wait for the process to complete naturally:
await process;

// Or to continue execution even if the command is still running:
process.continue();

// Later, if you need to get the unretrieved output:
const unretrievedOutput = terminalManager.getUnretrievedOutput(terminalId);
console.log('Unretrieved output:', unretrievedOutput);

Resources:
- https://github.com/microsoft/vscode/issues/226655
- https://code.visualstudio.com/updates/v1_93#_terminal-shell-integration-api
- https://code.visualstudio.com/docs/terminal/shell-integration
- https://code.visualstudio.com/api/references/vscode-api#Terminal
- https://github.com/microsoft/vscode-extension-samples/blob/main/terminal-sample/src/extension.ts
- https://github.com/microsoft/vscode-extension-samples/blob/main/shell-integration-sample/src/extension.ts
*/

/*
The new shellIntegration API gives us access to terminal command execution output handling.
However, we don't update our VSCode type definitions or engine requirements to maintain compatibility
with older VSCode versions. Users on older versions will automatically fall back to using sendText
for terminal command execution.
Interestingly, some environments like Cursor enable these APIs even without the latest VSCode engine.
This approach allows us to leverage advanced features when available while ensuring broad compatibility.
*/
declare module "vscode" {
	// https://github.com/microsoft/vscode/blob/f0417069c62e20f3667506f4b7e53ca0004b4e3e/src/vscode-dts/vscode.d.ts#L7442
	interface Terminal {
		shellIntegration?: {
			cwd?: vscode.Uri
			executeCommand?: (command: string) => {
				read: () => AsyncIterable<string>
			}
		}
	}
	// https://github.com/microsoft/vscode/blob/f0417069c62e20f3667506f4b7e53ca0004b4e3e/src/vscode-dts/vscode.d.ts#L10794
	interface Window {
		onDidStartTerminalShellExecution?: (
			listener: (e: any) => any,
			thisArgs?: any,
			disposables?: vscode.Disposable[],
		) => vscode.Disposable
	}
}

export class TerminalManager {
	private terminalIds: Set<number> = new Set()
	private processes: Map<number, TerminalProcess> = new Map()
	private disposables: vscode.Disposable[] = []
	private shellIntegrationTimeout: number = 4000
	private terminalReuseEnabled: boolean = true
	private terminalOutputLineLimit: number = 500
	private defaultTerminalProfile: string = "default"
	private serviceDetectionEnabled: boolean = true
	private serviceDetectionIdleTime: number = 10000 // 10秒
	private cacheService: any = null // 缓存服务，用于传递给 TerminalProcess

	// 🔑 新增：服务管理
	private runningServices: Map<string, { terminalProcess: TerminalProcess; terminal: vscode.Terminal }> = new Map()

	constructor(cacheService?: any) {
		this.cacheService = cacheService
		let disposable: vscode.Disposable | undefined
		try {
			disposable = (vscode.window as vscode.Window).onDidStartTerminalShellExecution?.(async (e) => {
				// Creating a read stream here results in a more consistent output. This is most obvious when running the `date` command.
				e?.execution?.read()
			})
		} catch (_error) {
			// console.error("Error setting up onDidEndTerminalShellExecution", error)
		}
		if (disposable) {
			this.disposables.push(disposable)
		}

		// Add a listener for terminal state changes to detect CWD updates
		try {
			const stateChangeDisposable = vscode.window.onDidChangeTerminalState((terminal) => {
				const terminalInfo = this.findTerminalInfoByTerminal(terminal)
				if (terminalInfo && terminalInfo.pendingCwdChange && terminalInfo.cwdResolved) {
					// Check if CWD has been updated to match the expected path
					if (this.isCwdMatchingExpected(terminalInfo)) {
						const resolver = terminalInfo.cwdResolved.resolve
						terminalInfo.pendingCwdChange = undefined
						terminalInfo.cwdResolved = undefined
						resolver()
					}
				}
			})
			this.disposables.push(stateChangeDisposable)
		} catch (error) {
			console.error("Error setting up onDidChangeTerminalState", error)
		}
	}

	//Find a TerminalInfo by its VSCode Terminal instance
	private findTerminalInfoByTerminal(terminal: vscode.Terminal): TerminalInfo | undefined {
		const terminals = TerminalRegistry.getAllTerminals()
		return terminals.find((t) => t.terminal === terminal)
	}

	//Check if a terminal's CWD matches its expected pending change
	private isCwdMatchingExpected(terminalInfo: TerminalInfo): boolean {
		if (!terminalInfo.pendingCwdChange) {
			return false
		}

		const currentCwd = terminalInfo.terminal.shellIntegration?.cwd?.fsPath
		const targetCwd = vscode.Uri.file(terminalInfo.pendingCwdChange).fsPath

		if (!currentCwd) {
			return false
		}

		return arePathsEqual(currentCwd, targetCwd)
	}

	runCommand(terminalInfo: TerminalInfo, command: string): TerminalProcessResultPromise {
		console.log(`[TerminalManager] Running command on terminal ${terminalInfo.id}: "${command}"`)
		console.log(`[TerminalManager] Terminal ${terminalInfo.id} busy state before: ${terminalInfo.busy}`)

		// 🔑 检查是否有相同服务正在运行
		const existingService = this.runningServices.get(command)
		if (existingService) {
			console.log(`[TerminalManager] 发现相同服务正在运行: "${command}"，重用终端并重启服务`)
			// 重用现有服务的终端，结束旧服务并重新执行
			return this.createReusedServiceProcess(existingService.terminalProcess, existingService.terminal, command)
		}

		// 重置终端状态，为新命令做准备
		terminalInfo.busy = true
		terminalInfo.lastCommand = command
		terminalInfo.isInteractive = false // 重置交互状态
		terminalInfo.interactiveType = undefined
		terminalInfo.isRunningService = false // 重置服务运行状态

		const process = new TerminalProcess(this.cacheService)
		this.processes.set(terminalInfo.id, process)

		process.once("completed", () => {
			// 更新状态
			terminalInfo.busy = false
			// 采集更多状态做详细日志
			const cwd = terminalInfo.terminal.shellIntegration?.cwd?.fsPath
			const proc = this.processes.get(terminalInfo.id)
			const isHot = proc ? proc.isHot : false
			const hasServiceRecord = this.hasRunningService(terminalInfo.lastCommand)
			console.log(
				`[TerminalManager] Terminal ${terminalInfo.id} completed. state={busy:false, isRunningService:${terminalInfo.isRunningService}, isInteractive:${terminalInfo.isInteractive}, interactiveType:${terminalInfo.interactiveType ?? ""}, lastCommand:"${terminalInfo.lastCommand}", cwd:"${cwd ?? ""}", shellPath:"${terminalInfo.shellPath ?? ""}", isHot:${isHot}, hasServiceRecordForLastCommand:${hasServiceRecord}}`,
			)
			// 注意：不要在 completed 时清理监听器，以免打断 continue 监听
		})

		// if shell integration is not available, remove terminal so it does not get reused as it may be running a long-running process
		process.once("no_shell_integration", () => {
			console.log(`no_shell_integration received for terminal ${terminalInfo.id}`)
			// Remove the terminal so we can't reuse it (in case it's running a long-running process)
			TerminalRegistry.removeTerminal(terminalInfo.id)
			this.terminalIds.delete(terminalInfo.id)
			this.processes.delete(terminalInfo.id)
		})

		// 🔑 修复：使用 once 避免重复触发，每次新命令会创建新的 process 实例
		process.once("service_detected", (lastOutput, serviceCommand) => {
			console.log(`[TerminalManager] 检测到服务程序在终端 ${terminalInfo.id}，命令: "${serviceCommand}"`)
			console.log(`[TerminalManager] 最后输出: ${lastOutput.slice(-100)}...`) // 只显示最后100字符
			console.log(`[TerminalManager] 服务程序正在运行，终端保持占用状态`)

			// 🔑 关键修复：服务程序检测后终端应该保持忙碌，因为服务还在运行
			terminalInfo.isInteractive = false // 服务程序不是交互式
			terminalInfo.interactiveType = undefined
			terminalInfo.busy = true // 保持忙碌状态，后续命令将使用新终端
			terminalInfo.isRunningService = true // 标记为正在运行服务

			// 🔑 新增：记录正在运行的服务
			this.runningServices.set(serviceCommand, {
				terminalProcess: process,
				terminal: terminalInfo.terminal,
			})

			console.log(`[TerminalManager] 终端 ${terminalInfo.id} 被服务程序占用，服务已注册: "${serviceCommand}"`)

			// 🔑 关键：服务检测后也要继续执行
			process.emit("completed")
			process.emit("continue")
		})

		// 🔑 修复：使用 once 避免重复触发，每次新命令会创建新的 process 实例
		process.once("interactive_detected", (lastOutput, promptType) => {
			console.log(`[TerminalManager] 检测到交互式程序在终端 ${terminalInfo.id}，提示符类型: ${promptType}`)
			console.log(`[TerminalManager] 最后输出: ${lastOutput.slice(-100)}...`)
			console.log(`[TerminalManager] 终端进入交互模式，后续命令将通过 stdin 注入`)

			// 标记终端为交互模式
			terminalInfo.isInteractive = true
			terminalInfo.interactiveType = promptType
			terminalInfo.busy = false // 允许后续命令注入

			// 发出继续事件，告诉模型可以继续执行
			process.emit("completed")
			process.emit("continue")
		})

		const promise = new Promise<void>((resolve, reject) => {
			process.once("continue", () => {
				resolve()
			})
			process.once("error", (error) => {
				console.error(`Error in terminal ${terminalInfo.id}:`, error)
				reject(error)
			})
		})

		// if shell integration is already active, run the command immediately
		if (terminalInfo.terminal.shellIntegration) {
			process.waitForShellIntegration = false
			process.run(terminalInfo.terminal, command)
		} else {
			// docs recommend waiting 3s for shell integration to activate
			console.log(
				`[TerminalManager Test] Waiting for shell integration for terminal ${terminalInfo.id} with timeout ${this.shellIntegrationTimeout}ms`,
			)
			pWaitFor(() => terminalInfo.terminal.shellIntegration !== undefined, {
				timeout: this.shellIntegrationTimeout,
			})
				.then(() => {
					console.log(
						`[TerminalManager Test] Shell integration activated for terminal ${terminalInfo.id} within timeout.`,
					)
				})
				.catch((err) => {
					console.warn(
						`[TerminalManager Test] Shell integration timed out or failed for terminal ${terminalInfo.id}: ${err.message}`,
					)
				})
				.finally(() => {
					console.log(`[TerminalManager Test] Proceeding with command execution for terminal ${terminalInfo.id}.`)
					const existingProcess = this.processes.get(terminalInfo.id)
					if (existingProcess && existingProcess.waitForShellIntegration) {
						existingProcess.waitForShellIntegration = false
						existingProcess.run(terminalInfo.terminal, command)
					}
				})
		}

		return mergePromise(process, promise)
	}

	/**
	 * 创建重用服务的进程包装器 - 结束旧服务并重新执行命令
	 */
	private createReusedServiceProcess(
		existingProcess: TerminalProcess,
		terminal: vscode.Terminal,
		command: string,
	): TerminalProcessResultPromise {
		console.log(`[TerminalManager] 重用现有服务终端，结束旧服务并重新执行: "${command}"`)

		// 创建新的进程来处理重新执行
		const reusedProcess = new TerminalProcess(this.cacheService)

		// 显示终端给用户
		terminal.show()

		// 安全校验：确认该终端里确实仍在运行“相同命令”的服务
		// 如果不是（例如之前误报/服务已停止），不发送 Ctrl+C，直接执行命令并清理陈旧记录
		try {
			if (!existingProcess.isRunningCommand(command)) {
				console.warn(`[TerminalManager] 检测到陈旧的服务记录（非同命令或已停止），跳过 Ctrl+C，直接执行: "${command}"`)
				this.runningServices.delete(command)
				// 直接在当前终端执行新命令
				reusedProcess.run(terminal, command)
				return reusedProcess as TerminalProcessResultPromise
			}
		} catch (e) {
			console.warn(`[TerminalManager] 服务状态校验失败，回退为直接执行: ${String(e)}`)
			this.runningServices.delete(command)
			reusedProcess.run(terminal, command)
			return reusedProcess as TerminalProcessResultPromise
		}

		// 异步处理重新执行逻辑（确认是同命令服务后，先发送 Ctrl+C 再重启）
		this.restartServiceInTerminal(terminal, command, reusedProcess)

		return reusedProcess as TerminalProcessResultPromise
	}

	/**
	 * 在现有终端中重启服务
	 */
	private async restartServiceInTerminal(terminal: vscode.Terminal, command: string, process: TerminalProcess) {
		try {
			console.log(`[TerminalManager] 发送中断信号结束当前服务`)

			// 🔑 先清除旧的服务记录
			this.runningServices.delete(command)

			// 发送 Ctrl+C 中断当前运行的服务
			terminal.sendText("\x03") // Ctrl+C

			// 等待一小段时间让服务完全停止
			await new Promise((resolve) => setTimeout(resolve, 1000))

			console.log(`[TerminalManager] 在重用终端中执行新命令: "${command}"`)

			// 🔑 为重启的服务添加监听器
			process.once("service_detected", (lastOutput, serviceCommand) => {
				console.log(`[TerminalManager] 重启后检测到服务: "${serviceCommand}"，重新注册到服务列表`)
				this.runningServices.set(serviceCommand, {
					terminalProcess: process,
					terminal: terminal,
				})
			})

			// 在同一终端中执行新命令
			await process.run(terminal, command)
		} catch (error) {
			console.error(`[TerminalManager] 重启服务失败:`, error)
			process.emit("error", error as Error)
		}
	}

	async getOrCreateTerminal(cwd: string): Promise<TerminalInfo> {
		const terminals = TerminalRegistry.getAllTerminals()
		const expectedShellPath =
			this.defaultTerminalProfile !== "default" ? getShellForProfile(this.defaultTerminalProfile) : undefined

		// Find available terminal from our pool first (created for this task)
		console.log(`[TerminalManager] Looking for terminal in cwd: ${cwd}`)
		console.log(`[TerminalManager] Available terminals: ${terminals.length}`)

		const matchingTerminal = terminals.find((t) => {
			if (t.busy) {
				console.log(`[TerminalManager] Terminal ${t.id} is busy, skipping`)
				return false
			}
			// 🔑 关键修复：跳过正在运行服务的终端
			if (t.isRunningService) {
				console.log(`[TerminalManager] Terminal ${t.id} is running a service, skipping`)
				return false
			}
			// Check if shell path matches current configuration
			if (t.shellPath !== expectedShellPath) {
				return false
			}
			const terminalCwd = t.terminal.shellIntegration?.cwd // one of cline's commands could have changed the cwd of the terminal
			if (!terminalCwd) {
				console.log(`[TerminalManager] Terminal ${t.id} has no cwd, skipping`)
				return false
			}
			const matches = arePathsEqual(vscode.Uri.file(cwd).fsPath, terminalCwd.fsPath)
			console.log(`[TerminalManager] Terminal ${t.id} cwd: ${terminalCwd.fsPath}, matches: ${matches}`)
			return matches
		})
		if (matchingTerminal) {
			console.log(`[TerminalManager] Found matching terminal ${matchingTerminal.id} in correct cwd`)
			this.terminalIds.add(matchingTerminal.id)
			return matchingTerminal
		}

		// If no non-busy terminal in the current working dir exists and terminal reuse is enabled, try to find any non-busy terminal regardless of CWD
		if (this.terminalReuseEnabled) {
			const availableTerminal = terminals.find((t) => !t.busy && !t.isRunningService && t.shellPath === expectedShellPath)
			if (availableTerminal) {
				// Set up promise and tracking for CWD change
				const cwdPromise = new Promise<void>((resolve, reject) => {
					availableTerminal.pendingCwdChange = cwd
					availableTerminal.cwdResolved = { resolve, reject }
				})

				// Navigate back to the desired directory
				const cdProcess = this.runCommand(availableTerminal, `cd "${cwd}"`)

				// Wait for the cd command to complete before proceeding
				await cdProcess

				// Add a small delay to ensure terminal is ready after cd
				await new Promise((resolve) => setTimeout(resolve, 100))

				// Either resolve immediately if CWD already updated or wait for event/timeout
				if (this.isCwdMatchingExpected(availableTerminal)) {
					if (availableTerminal.cwdResolved) {
						availableTerminal.cwdResolved.resolve()
					}
					availableTerminal.pendingCwdChange = undefined
					availableTerminal.cwdResolved = undefined
				} else {
					try {
						// Wait with a timeout for state change event to resolve
						await Promise.race([
							cwdPromise,
							new Promise<void>((_, reject) =>
								setTimeout(() => reject(new Error(`CWD timeout: Failed to update to ${cwd}`)), 1000),
							),
						])
					} catch (_err) {
						// Clear pending state on timeout
						availableTerminal.pendingCwdChange = undefined
						availableTerminal.cwdResolved = undefined
					}
				}
				this.terminalIds.add(availableTerminal.id)
				return availableTerminal
			}
		}

		// If all terminals are busy or don't match shell profile, create a new one with the configured shell
		const newTerminalInfo = TerminalRegistry.createTerminal(cwd, expectedShellPath)
		this.terminalIds.add(newTerminalInfo.id)
		return newTerminalInfo
	}

	getTerminals(busy: boolean): { id: number; lastCommand: string }[] {
		return Array.from(this.terminalIds)
			.map((id) => TerminalRegistry.getTerminal(id))
			.filter((t): t is TerminalInfo => t !== undefined && t.busy === busy)
			.map((t) => ({ id: t.id, lastCommand: t.lastCommand }))
	}

	getUnretrievedOutput(terminalId: number): string {
		if (!this.terminalIds.has(terminalId)) {
			return ""
		}
		const process = this.processes.get(terminalId)
		return process ? process.getUnretrievedOutput() : ""
	}

	isProcessHot(terminalId: number): boolean {
		const process = this.processes.get(terminalId)
		return process ? process.isHot : false
	}

	/**
	 * Send input to a running process in a terminal
	 * @param terminalId The terminal ID
	 * @param input The input text to send
	 * @param addNewLine Whether to add a newline at the end (default: true)
	 * @returns true if input was sent successfully, false if terminal/process not found
	 */
	sendInputToProcess(terminalId: number, input: string, addNewLine: boolean = true): boolean {
		if (!this.terminalIds.has(terminalId)) {
			return false
		}
		const process = this.processes.get(terminalId)
		if (process) {
			process.sendInput(input, addNewLine)
			return true
		}
		return false
	}

	/**
	 * 检查终端是否处于交互模式
	 * @param terminalId The terminal ID
	 * @returns true if terminal is in interactive mode
	 */
	isTerminalInteractive(terminalId: number): boolean {
		const terminalInfo = TerminalRegistry.getTerminal(terminalId)
		if (!terminalInfo) {
			return false
		}
		// 检查终端是否被明确标记为交互模式
		return terminalInfo.isInteractive === true && this.processes.has(terminalId)
	}

	/**
	 * 尝试在现有交互式终端中执行命令，如果没有则创建新终端
	 * @param command The command to execute
	 * @param cwd Working directory
	 * @returns TerminalProcessResultPromise
	 */
	async runCommandOrInject(command: string, cwd: string): Promise<TerminalProcessResultPromise> {
		// 查找是否有交互式终端可以复用
		const terminals = TerminalRegistry.getAllTerminals()
		const interactiveTerminal = terminals.find(
			(t) => this.isTerminalInteractive(t.id) && t.terminal.shellIntegration?.cwd?.fsPath === cwd,
		)

		if (interactiveTerminal) {
			console.log(`[TerminalManager] 在交互式终端 ${interactiveTerminal.id} 中注入命令: "${command}"`)
			console.log(`[TerminalManager] 交互式类型: ${interactiveTerminal.interactiveType}`)

			// 直接发送命令到交互式终端
			this.sendInputToProcess(interactiveTerminal.id, command)

			// 返回现有的进程对象
			const existingProcess = this.processes.get(interactiveTerminal.id)
			if (existingProcess) {
				return existingProcess as TerminalProcessResultPromise
			}
		}

		// 没有合适的交互式终端，创建新的
		console.log(`[TerminalManager] 没有找到合适的交互式终端，创建新终端执行命令: "${command}"`)
		const terminalInfo = await this.getOrCreateTerminal(cwd)
		return this.runCommand(terminalInfo, command)
	}

	disposeAll() {
		// for (const info of this.terminals) {
		// 	//info.terminal.dispose() // dont want to dispose terminals when task is aborted
		// }
		this.terminalIds.clear()
		this.processes.clear()
		this.runningServices.clear() // 🔑 清理服务记录
		this.disposables.forEach((disposable) => disposable.dispose())
		this.disposables = []
	}

	setShellIntegrationTimeout(timeout: number): void {
		this.shellIntegrationTimeout = timeout
	}

	/**
	 * 获取当前运行的服务列表
	 */
	getRunningServices(): Array<{ command: string; terminalName: string }> {
		const services = []
		for (const [command, service] of this.runningServices.entries()) {
			services.push({
				command,
				terminalName: service.terminal.name,
			})
		}
		return services
	}

	/**
	 * 检查指定命令是否有服务正在运行
	 */
	hasRunningService(command: string): boolean {
		return this.runningServices.has(command)
	}

	setTerminalReuseEnabled(enabled: boolean): void {
		this.terminalReuseEnabled = enabled
	}

	setTerminalOutputLineLimit(limit: number): void {
		this.terminalOutputLineLimit = limit
	}

	setServiceDetectionEnabled(enabled: boolean): void {
		this.serviceDetectionEnabled = enabled
	}

	setServiceDetectionIdleTime(idleTime: number): void {
		this.serviceDetectionIdleTime = idleTime
	}

	public processOutput(outputLines: string[]): string {
		if (outputLines.length > this.terminalOutputLineLimit) {
			const halfLimit = Math.floor(this.terminalOutputLineLimit / 2)
			const start = outputLines.slice(0, halfLimit)
			const end = outputLines.slice(outputLines.length - halfLimit)
			return `${start.join("\n")}\n... (output truncated) ...\n${end.join("\n")}`.trim()
		}
		return outputLines.join("\n").trim()
	}

	setDefaultTerminalProfile(profileId: string): { closedCount: number; busyTerminals: TerminalInfo[] } {
		// Only handle terminal change if profile actually changed
		if (this.defaultTerminalProfile === profileId) {
			return { closedCount: 0, busyTerminals: [] }
		}

		const _oldProfileId = this.defaultTerminalProfile
		this.defaultTerminalProfile = profileId

		// Get the shell path for the new profile
		const newShellPath = profileId !== "default" ? getShellForProfile(profileId) : undefined

		// Handle terminal management for the profile change
		const result = this.handleTerminalProfileChange(newShellPath)

		// Update lastActive for any remaining terminals
		const allTerminals = TerminalRegistry.getAllTerminals()
		allTerminals.forEach((terminal) => {
			if (terminal.shellPath !== newShellPath) {
				TerminalRegistry.updateTerminal(terminal.id, { lastActive: Date.now() })
			}
		})

		return result
	}

	/**
	 * Filters terminals based on a provided criteria function
	 * @param filterFn Function that accepts TerminalInfo and returns boolean
	 * @returns Array of terminals that match the criteria
	 */
	filterTerminals(filterFn: (terminal: TerminalInfo) => boolean): TerminalInfo[] {
		const terminals = TerminalRegistry.getAllTerminals()
		return terminals.filter(filterFn)
	}

	/**
	 * Closes terminals that match the provided criteria
	 * @param filterFn Function that accepts TerminalInfo and returns boolean for terminals to close
	 * @param force If true, closes even busy terminals (with warning)
	 * @returns Number of terminals closed
	 */
	closeTerminals(filterFn: (terminal: TerminalInfo) => boolean, force: boolean = false): number {
		const terminalsToClose = this.filterTerminals(filterFn)
		let closedCount = 0

		for (const terminalInfo of terminalsToClose) {
			// Skip busy terminals unless force is true
			if (terminalInfo.busy && !force) {
				continue
			}

			// Remove from our tracking
			if (this.terminalIds.has(terminalInfo.id)) {
				this.terminalIds.delete(terminalInfo.id)
			}
			this.processes.delete(terminalInfo.id)

			// 🔑 清理该终端对应的服务记录
			for (const [command, service] of this.runningServices.entries()) {
				if (service.terminal === terminalInfo.terminal) {
					console.log(`[TerminalManager] 清理终端关闭的服务记录: "${command}"`)
					this.runningServices.delete(command)
					break
				}
			}

			// Dispose the actual terminal
			terminalInfo.terminal.dispose()

			// Remove from registry
			TerminalRegistry.removeTerminal(terminalInfo.id)

			closedCount++
		}

		return closedCount
	}

	/**
	 * Handles terminal management when the terminal profile changes
	 * @param newShellPath New shell path to use
	 * @returns Object with information about closed terminals and remaining busy terminals
	 */
	handleTerminalProfileChange(newShellPath: string | undefined): {
		closedCount: number
		busyTerminals: TerminalInfo[]
	} {
		// Close non-busy terminals with different shell path
		const closedCount = this.closeTerminals((terminal) => !terminal.busy && terminal.shellPath !== newShellPath, false)

		// Get remaining busy terminals with different shell path
		const busyTerminals = this.filterTerminals((terminal) => terminal.busy && terminal.shellPath !== newShellPath)

		return {
			closedCount,
			busyTerminals,
		}
	}

	/**
	 * Forces closure of all terminals (including busy ones)
	 * @returns Number of terminals closed
	 */
	closeAllTerminals(): number {
		return this.closeTerminals(() => true, true)
	}

	/**
	 * Gets the type of the default terminal based on its shell path
	 * @returns The terminal type (shell name) or 'unknown' if not determinable
	 */
	getTerminalType(): string {
		// First, try to get the expected shell path from configuration
		const expectedShellPath =
			this.defaultTerminalProfile !== "default"
				? getShellForProfile(this.defaultTerminalProfile)
				: getShellForProfile("default")

		if (expectedShellPath) {
			const shellName = this.extractShellName(expectedShellPath)
			if (shellName) {
				return shellName
			}
		}

		// Get all terminals and find the default one based on current configuration
		const terminals = TerminalRegistry.getAllTerminals()

		if (terminals.length > 0) {
			// Find a terminal that matches the expected shell path, or get the first available terminal
			const terminalInfo = terminals.find((t) => t.shellPath === expectedShellPath) || terminals[0]

			if (terminalInfo && terminalInfo.shellPath) {
				const shellName = this.extractShellName(terminalInfo.shellPath)
				if (shellName) {
					return shellName
				}
			}
		}

		// If no terminals exist or they don't have shell paths, try creating a temporary terminal
		if (terminals.length === 0) {
			try {
				// Fallback: create a temporary terminal to detect the shell
				const tempTerminal = TerminalRegistry.createTerminal()
				const shellPath = tempTerminal.shellPath

				// Clean up the temporary terminal
				TerminalRegistry.removeTerminal(tempTerminal.id)

				if (shellPath) {
					const shellName = this.extractShellName(shellPath)
					if (shellName) {
						return shellName
					}
				}
			} catch (error) {
				console.error("Error creating temporary terminal for type detection:", error)
			}
		}

		// Final fallback: try to get shell from environment variables
		let envShell: string | null = null
		if (process.platform === "win32") {
			envShell = process.env.COMSPEC || "cmd.exe"
		} else {
			envShell = process.env.SHELL || "/bin/bash"
		}

		if (envShell) {
			const shellName = this.extractShellName(envShell)
			if (shellName) {
				return shellName
			}
		}

		return "unknown"
	}

	/**
	 * Extracts the shell name from a shell path
	 * @param shellPath The full path to the shell executable
	 * @returns The shell name (e.g., 'bash', 'pwsh', 'zsh') or empty string if not found
	 */
	private extractShellName(shellPath: string): string {
		if (!shellPath) {
			return ""
		}

		// Get the basename of the shell path
		const pathParts = shellPath.split(/[/\\]/)
		const shellName = pathParts[pathParts.length - 1].toLowerCase()

		// Remove common extensions
		const baseName = shellName.replace(/\.(exe|cmd|bat)$/i, "")

		// Normalize shell names to model-friendly names
		switch (baseName) {
			case "pwsh":
			case "powershell":
				return "powershell"
			case "cmd":
				return "cmd"
			case "bash":
				return "bash"
			case "zsh":
				return "zsh"
			case "fish":
				return "fish"
			case "sh":
				return "sh"
			case "csh":
			case "tcsh":
				return "csh"
			case "ksh":
				return "ksh"
			default:
				return baseName
		}
	}
}
