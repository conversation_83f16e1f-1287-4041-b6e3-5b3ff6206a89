import { EventEmitter } from "events"
import * as vscode from "vscode"
import { stripAnsi } from "./ansiUtils"
import { getLatestTerminalOutput } from "./get-latest-output"

// ================= Performance-oriented precompiled detectors =================
// 🔑 配置项：从全局状态和 VSCode 设置中获取，支持用户自定义
function getTerminalDetectionConfig(cacheService?: any) {
	// 优先从全局状态读取，如果没有则从 VSCode 配置读取
	if (cacheService) {
		return {
			maxAttempts:
				cacheService.getGlobalStateKey("terminalDetectionMaxAttempts") ??
				vscode.workspace.getConfiguration().get<number>("cline.terminal.detection.maxAttempts", 10),
			idleTimeMs:
				cacheService.getGlobalStateKey("terminalDetectionIdleTimeMs") ??
				vscode.workspace.getConfiguration().get<number>("cline.terminal.detection.idleTimeMs", 10000),
			maxOutputChars:
				cacheService.getGlobalStateKey("terminalDetectionMaxOutputChars") ??
				vscode.workspace.getConfiguration().get<number>("cline.terminal.detection.maxOutputChars", 65536),
			maxLastLines:
				cacheService.getGlobalStateKey("terminalDetectionMaxLastLines") ??
				vscode.workspace.getConfiguration().get<number>("cline.terminal.detection.maxLastLines", 10),
		}
	} else {
		// 兜底：直接从 VSCode 配置读取
		const config = vscode.workspace.getConfiguration()
		return {
			maxAttempts: config.get<number>("cline.terminal.detection.maxAttempts", 10),
			idleTimeMs: config.get<number>("cline.terminal.detection.idleTimeMs", 10000),
			maxOutputChars: config.get<number>("cline.terminal.detection.maxOutputChars", 65536),
			maxLastLines: config.get<number>("cline.terminal.detection.maxLastLines", 10),
		}
	}
}

// Precompiled service detection regexes (compiled once)
const EN_SERVICE_REGEXES: RegExp[] = [
	/server\s+(running|started|listening)/i,
	/listening\s+on\s+(port|http)/i,
	/press\s+(any\s+key|ctrl\+c)\s+to/i,
	/waiting\s+for\s+(connections|requests)/i,
	/ready\s+on\s+http/i,
	/local:\s+http/i,
	/webpack\s+compiled/i,
	/development\s+server\s+started/i,
	/serving\s+at\s+http/i,
	/app\s+running\s+at/i,
	/started\s+server\s+on/i,
	/server\s+is\s+(running|listening)/i,
	/application\s+(started|running)/i,
	/service\s+(started|running)/i,
	// 🔑 新增：现代开发服务器检测模式
	/vite\s+.*?\s+ready\s+in/i, // VITE v5.4.19 ready in 2450 ms
	/➜\s+local:\s+http/i, // ➜ Local: http://localhost:5173/
	/➜\s+network:/i, // ➜ Network: use --host to expose
	/press\s+h\s+\+\s+enter\s+to\s+show\s+help/i, // press h + enter to show help
	/ready\s+-\s+started\s+server\s+on/i, // ready - started server on 0.0.0.0:3000
	/next\.js\s+.*?\s+ready/i, // Next.js ready
	/compiled\s+successfully/i, // compiled successfully
	/webpack\s+.*?\s+compiled/i, // webpack compiled
	/dev\s+server\s+running/i, // dev server running
	/development\s+server\s+is\s+running/i, // development server is running
	/development\s+server\s+.*?\s+listening/i, // Angular Live Development Server is listening
	/server\s+started\s+at/i, // server started at
	/app\s+running\s+at:/i, // App running at:
	/-\s+local:\s+http/i, // - Local: http://localhost:8080/
]
const ZH_SERVICE_REGEXES: RegExp[] = [
	/服务(器)?.*?(已经|正在).*?(运行|启动|监听)/,
	/.*?(Web|HTTP|数据库|应用).*?服务.*?(已|正在).*?(启动|运行)/,
	/.*?(服务器|开发服务器).*?(启动|运行).*?成功/,
	/.*?监听.*?(端口|地址|http)/,
	/.*?服务器.*?(就绪|准备就绪)/,
	/.*?应用.*?(已启动|正在运行)/,
	/.*?开发服务器.*?(启动|运行)/,
	/.*?(Web|HTTP).*?服务.*?(启动|运行)/,
	/按.*?(任意键|Ctrl\+C).*?(停止|退出)/,
	/.*?等待.*?(连接|请求)/,
	/.*?准备.*?接受.*?(连接|请求)/,
	/.*?服务.*?可用/,
	/.*?访问.*?http/,
	/.*?监听.*?端口.*?\d+/,
	/.*?服务.*?(已|正在).*?监听/,
	/.*?(启动|运行).*?在.*?端口/,
]

// Low-cost keyword short-circuit before regex
const SERVICE_KEYWORDS_EN = [
	"server",
	"listening",
	"http",
	"port",
	"compiled",
	"press",
	"ready",
	"running",
	// 🔑 新增：现代开发服务器关键词
	"vite",
	"local:",
	"network:",
	"next.js",
	"webpack",
	"localhost",
	"dev",
	"development",
	"angular",
	"started",
	"app",
]
const SERVICE_KEYWORDS_ZH = ["服务", "服务器", "运行", "启动", "监听", "访问", "端口", "就绪", "请求", "连接"]

// Precompiled interactive prompt regexes (compiled once)
const INTERACTIVE_REGEXES: Array<{ pattern: RegExp; type: string }> = [
	{ pattern: /^>>>\s*$/, type: "python_repl" },
	{ pattern: /^>>\s*$/, type: "python_continue" },
	{ pattern: /irb\(\w+\):\d+:\d+>\s*$/, type: "ruby_irb" },
	{ pattern: /^>\s*$/, type: "node_repl" },
	{ pattern: /^\$\s*$/, type: "shell_prompt" },
	{ pattern: /^#\s*$/, type: "root_shell" },
	{ pattern: /^C:\\.*>\s*$/, type: "cmd_prompt" },
	// 通用命令行提示符模式 xxx> 和 xxx >
	{ pattern: /^[a-zA-Z0-9_\-.]+>\s*$/, type: "generic_command_prompt" },
	{ pattern: /^[a-zA-Z0-9_\-.\s]+>\s*$/, type: "generic_command_prompt_with_space" },
	// 中文输入提示
	{ pattern: /请输入.*[:：]\s*$/, type: "chinese_input" },
	{ pattern: /输入.*[:：]\s*$/, type: "chinese_input" },
	{ pattern: /请选择.*[:：]\s*$/, type: "chinese_choice" },
	{ pattern: /选择.*[:：]\s*$/, type: "chinese_choice" },
	{ pattern: /确认.*[:：?]\s*$/, type: "chinese_confirm" },
	{ pattern: /密码[:：]\s*$/, type: "chinese_password" },
	{ pattern: /用户名[:：]\s*$/, type: "chinese_username" },
	{ pattern: /继续.*\?\s*$/, type: "chinese_continue" },
	{ pattern: /是否.*\?\s*$/, type: "chinese_yes_no" },
	{ pattern: /.*确认.*\?\s*$/, type: "chinese_confirm_question" },
	{ pattern: /\(是\/否\)\s*\??\s*$/, type: "chinese_yes_no_prompt" },
	{ pattern: /\(y\/n\)\s*\??\s*$/i, type: "yes_no_prompt" },
	// 英文输入提示
	{ pattern: /enter\s+.*[:：]\s*$/i, type: "english_input" },
	{ pattern: /input\s+.*[:：]\s*$/i, type: "input_request" },
	{ pattern: /continue\s*\?\s*$/i, type: "continue_prompt" },
	{ pattern: /password\s*[:：]\s*$/i, type: "password_prompt" },
	{ pattern: /username\s*[:：]\s*$/i, type: "username_prompt" },
	{ pattern: /confirm\s*[:：]\s*$/i, type: "confirm_prompt" },
]

export interface TerminalProcessEvents {
	line: [line: string]
	continue: []
	completed: []
	error: [error: Error]
	no_shell_integration: []
	service_detected: [lastOutput: string, command: string] // 🔑 新增命令参数
	interactive_detected: [lastOutput: string, promptType: string]
}

// how long to wait after a process outputs anything before we consider it "cool" again
const PROCESS_HOT_TIMEOUT_NORMAL = 2_000
const PROCESS_HOT_TIMEOUT_COMPILING = 15_000

// how long to wait for output before considering the process might be a long-running service
// 注意：现在从配置中获取，这里保留作为兜底值
const SERVICE_DETECTION_TIMEOUT = 20_000 // 兜底值：20秒无输出认为是服务程序

export class TerminalProcess extends EventEmitter<TerminalProcessEvents> {
	waitForShellIntegration: boolean = true
	private isListening: boolean = true
	private buffer: string = ""
	private fullOutput: string = ""
	private lastRetrievedIndex: number = 0
	isHot: boolean = false
	private hotTimer: NodeJS.Timeout | null = null
	private serviceDetectionTimer: NodeJS.Timeout | null = null
	private terminal: vscode.Terminal | null = null
	private classificationEmitted: boolean = false
	private detectionAttempts: number = 0
	private detectionConfig = getTerminalDetectionConfig() // 获取配置项
	private cacheService: any = null // 缓存服务，用于读取全局状态

	// 🔑 新增：服务命令记录
	private currentCommand: string = "" // 当前执行的命令
	private isServiceRunning: boolean = false // 是否正在运行服务

	constructor(cacheService?: any) {
		super()
		this.cacheService = cacheService
		this.detectionConfig = getTerminalDetectionConfig(cacheService)
	}

	/**
	 * 检测最后的输出是否表明这是一个服务程序
	 */
	private isServiceProgram(lastOutput: string): boolean {
		// 🔍 调试信息：记录检测过程
		console.log(`[TerminalProcess] 服务检测 - 输出长度: ${lastOutput.length} 字符`)
		console.log(`[TerminalProcess] 服务检测 - 最后100字符: "${lastOutput.slice(-100)}"`)

		// 优先使用低成本关键词短路
		const fastLower = lastOutput.toLowerCase()
		let fastHit = false
		const matchedKeywords = []
		for (const kw of SERVICE_KEYWORDS_EN) {
			if (fastLower.includes(kw)) {
				fastHit = true
				matchedKeywords.push(kw)
			}
		}
		if (!fastHit) {
			for (const kw of SERVICE_KEYWORDS_ZH) {
				if (lastOutput.includes(kw)) {
					fastHit = true
					matchedKeywords.push(kw)
				}
			}
		}

		console.log(`[TerminalProcess] 服务检测 - 匹配关键词: [${matchedKeywords.join(", ")}]`)
		if (!fastHit) {
			console.log(`[TerminalProcess] 服务检测 - 关键词检测失败`)
			return false
		}

		// 🔑 关键修复：过滤空行，只取有内容的最后几行
		const allLines = lastOutput.split("\n")
		const nonEmptyLines = allLines.filter((line) => line.trim().length > 0)
		const lastLines = nonEmptyLines.slice(-this.detectionConfig.maxLastLines)

		console.log(`[TerminalProcess] 服务检测 - 总行数: ${allLines.length}, 非空行数: ${nonEmptyLines.length}`)
		console.log(`[TerminalProcess] 服务检测 - 分析最后 ${lastLines.length} 个非空行`)
		lastLines.forEach((line, index) => {
			console.log(`[TerminalProcess] 服务检测 - 非空行${index + 1}: "${line}"`)
		})

		// 英文/中文正则数组在模块级预编译
		const hasEnglishPattern = EN_SERVICE_REGEXES.some((pattern) => {
			const matches = lastLines.some((line) => pattern.test(line))
			if (matches) {
				console.log(`[TerminalProcess] 服务检测 - 匹配正则: ${pattern.toString()}`)
			}
			return matches
		})
		if (hasEnglishPattern) {
			console.log(`[TerminalProcess] 服务检测 - ✅ 检测到服务程序（英文模式）`)
			return true
		}

		const hasChinesePattern = ZH_SERVICE_REGEXES.some((pattern) => {
			const matches = lastLines.some((line) => pattern.test(line))
			if (matches) {
				console.log(`[TerminalProcess] 服务检测 - 匹配正则: ${pattern.toString()}`)
			}
			return matches
		})

		if (hasChinesePattern) {
			console.log(`[TerminalProcess] 服务检测 - ✅ 检测到服务程序（中文模式）`)
		} else {
			console.log(`[TerminalProcess] 服务检测 - ❌ 未检测到服务程序`)
		}

		return hasChinesePattern
	}

	/**
	 * 检测最后的输出是否表明这是一个交互式程序
	 */
	private isInteractiveProgram(lastOutput: string): { isInteractive: boolean; promptType: string } {
		const lastLine = (lastOutput.split("\n").pop() || "").trimEnd()
		const lastChar = lastLine.slice(-1)

		// 低成本快速路径：如果末尾不是典型提示符字符，直接返回
		const quickTailChars = new Set([":", ">", "?", "$", "#"]) // 常见提示符结尾
		if (!quickTailChars.has(lastChar)) {
			return { isInteractive: false, promptType: "" }
		}

		// 正则匹配使用模块级预编译表
		for (const { pattern, type } of INTERACTIVE_REGEXES) {
			if (pattern.test(lastLine)) {
				return { isInteractive: true, promptType: type }
			}
		}

		// 更保守的兜底：短行且含提示尾字符，且不包含明显的非提示关键词
		if (lastLine.length <= 5 && !/http|port|server/i.test(lastLine)) {
			return { isInteractive: true, promptType: "generic_prompt" }
		}
		return { isInteractive: false, promptType: "" }
	}

	async run(terminal: vscode.Terminal, command: string) {
		this.terminal = terminal
		this.currentCommand = command
		this.isServiceRunning = false

		console.log(`[TerminalProcess] 执行命令: "${command}"`)

		// When command does not produce any output, we can assume the shell integration API failed and as a fallback return the current terminal contents
		const returnCurrentTerminalContents = async () => {
			try {
				const terminalSnapshot = await getLatestTerminalOutput()
				if (terminalSnapshot && terminalSnapshot.trim()) {
					const fallbackMessage = `The command's output could not be captured due to some technical issue, however it has been executed successfully. Here's the current terminal's content to help you get the command's output:\n\n${terminalSnapshot}`
					this.emit("line", fallbackMessage)
				}
			} catch (error) {
				console.error("Error capturing terminal output:", error)
			}
		}

		// Start timer to detect if process is a long-running service or interactive program
		const startServiceDetectionTimer = () => {
			if (this.serviceDetectionTimer) {
				clearTimeout(this.serviceDetectionTimer)
			}
			this.serviceDetectionTimer = setTimeout(() => {
				// 达到 idleTime，无新输出，进行一次智能判定
				this.detectionAttempts++
				console.log(`[TerminalProcess] 空闲超时，开始智能判定程序类型 (第${this.detectionAttempts}次尝试)`)

				// 避免重复多次发射分类事件
				if (this.classificationEmitted) return

				const interactiveResult = this.isInteractiveProgram(this.fullOutput)
				if (interactiveResult.isInteractive) {
					console.log(`[TerminalProcess] 检测到交互式程序，提示符类型: ${interactiveResult.promptType}`)
					this.classificationEmitted = true
					this.emit("interactive_detected", this.fullOutput, interactiveResult.promptType)
					this.cleanupTimers() // 🔑 只清理定时器，保持状态稳定
					this.emit("completed")
					this.emit("continue")
					return
				}

				const isService = this.isServiceProgram(this.fullOutput)
				if (isService) {
					console.log(`[TerminalProcess] 检测到服务程序: "${this.currentCommand}"，继续执行后续任务`)
					this.classificationEmitted = true
					this.isServiceRunning = true // 🔑 标记服务正在运行
					this.emit("service_detected", this.fullOutput, this.currentCommand) // 🔑 传递命令信息
					this.cleanupTimers() // 🔑 只清理定时器，保持状态稳定
					this.emit("completed")
					this.emit("continue")
				} else {
					// 🔑 关键修复：如果既不是服务也不是交互式，根据尝试次数决定是否继续
					if (this.detectionAttempts < this.detectionConfig.maxAttempts) {
						console.log(
							`[TerminalProcess] 未能确定程序类型，继续等待更多输出... (${this.detectionAttempts}/${this.detectionConfig.maxAttempts})`,
						)
						// 重新启动检测计时器，继续监听
						startServiceDetectionTimer()
					} else {
						console.log(`[TerminalProcess] 达到最大检测次数，按普通命令处理`)
						// 达到最大尝试次数，按普通命令处理
						this.classificationEmitted = true
						this.cleanupTimers() // 🔑 只清理定时器，保持状态稳定
						this.emit("continue") // 自动continue， 有的人不会点proceed while processing
					}
				}
			}, this.detectionConfig.idleTimeMs)
		}

		const resetServiceDetectionTimer = () => {
			if (this.serviceDetectionTimer) {
				clearTimeout(this.serviceDetectionTimer)
				this.serviceDetectionTimer = null
			}
			// 🔑 重置检测尝试次数，因为有新输出表明程序还在活跃
			this.detectionAttempts = 0
		}

		if (terminal.shellIntegration && terminal.shellIntegration.executeCommand) {
			console.log(`[TerminalProcess] 使用Shell Integration执行: "${command}"`)

			const execution = terminal.shellIntegration.executeCommand(command)
			const stream = execution.read()
			// todo: need to handle errors
			let isFirstChunk = true
			let didOutputNonCommand = false
			let didEmitEmptyLine = false

			// Start the service detection timer
			startServiceDetectionTimer()

			for await (let data of stream) {
				// Reset the service detection timer since we got output
				resetServiceDetectionTimer()
				// 1. Process chunk and remove artifacts
				if (isFirstChunk) {
					/*
					The first chunk we get from this stream needs to be processed to be more human readable, ie remove vscode's custom escape sequences and identifiers, removing duplicate first char bug, etc.
					*/

					// bug where sometimes the command output makes its way into vscode shell integration metadata
					/*
					]633 is a custom sequence number used by VSCode shell integration:
					- OSC 633 ; A ST - Mark prompt start
					- OSC 633 ; B ST - Mark prompt end
					- OSC 633 ; C ST - Mark pre-execution (start of command output)
					- OSC 633 ; D [; <exitcode>] ST - Mark execution finished with optional exit code
					- OSC 633 ; E ; <commandline> [; <nonce>] ST - Explicitly set command line with optional nonce
					*/
					// if you print this data you might see something like "eecho hello worldo hello world;5ba85d14-e92a-40c4-b2fd-71525581eeb0]633;C" but this is actually just a bunch of escape sequences, ignore up to the first ;C
					/* ddateb15026-6a64-40db-b21f-2a621a9830f0]633;CTue Sep 17 06:37:04 EDT 2024 % ]633;D;0]633;P;Cwd=/Users/<USER>/Repositories/test */
					// Gets output between ]633;C (command start) and ]633;D (command end)
					const outputBetweenSequences = this.removeLastLineArtifacts(
						data.match(/\]633;C([\s\S]*?)\]633;D/)?.[1] || "",
					).trim()

					// Once we've retrieved any potential output between sequences, we can remove everything up to end of the last sequence
					// https://code.visualstudio.com/docs/terminal/shell-integration#_vs-code-custom-sequences-osc-633-st
					const vscodeSequenceRegex = /\x1b\]633;.[^\x07]*\x07/g
					let lastMatch: RegExpExecArray | null = null
					let match: RegExpExecArray | null
					while ((match = vscodeSequenceRegex.exec(data)) !== null) {
						lastMatch = match
					}
					if (lastMatch && lastMatch.index !== undefined) {
						data = data.slice(lastMatch.index + lastMatch[0].length)
					}
					// Place output back after removing vscode sequences
					if (outputBetweenSequences) {
						data = outputBetweenSequences + "\n" + data
					}
					// remove ansi
					data = stripAnsi(data)
					// Remove generic OSC sequences (like setting window/tab title) and any leftover BELs
					// This prevents lines like "管理员: C:\\WINDOWS\\system32\\cmd.exe\x07" from leaking into output
					//data = data.replace(/\x1b\][^\x07]*\x07/g, "")  从1b到07中间所有都没了。
					//data = data.replace(/\x07/g, "")
					// Trim extremely long chunks to avoid unbounded growth
					if (data.length > this.detectionConfig.maxOutputChars) {
						data = data.slice(-this.detectionConfig.maxOutputChars)
					}
					// Split data by newlines
					const lines = data ? data.split("\n") : []
					// Remove non-human readable characters from the first line
					if (lines.length > 0) {
						lines[0] = lines[0].replace(/[^\x20-\x7E]/g, "")
					}
					// Check for duplicated first character that might be a terminal artifact
					// But skip this check for known syntax characters like {, [, ", etc.
					if (
						lines.length > 0 &&
						lines[0].length >= 2 &&
						lines[0][0] === lines[0][1] &&
						!["[", "{", '"', "'", "<", "("].includes(lines[0][0])
					) {
						lines[0] = lines[0].slice(1)
					}
					// Only remove specific terminal artifacts from line beginnings while preserving JSON syntax
					if (lines.length > 0) {
						// This regex only removes common terminal artifacts (%, $, >, #) and invisible control chars
						// but preserves important syntax chars like {, [, ", etc.
						lines[0] = lines[0].replace(/^[\x00-\x1F%$>#\s]*/, "")
					}
					if (lines.length > 1) {
						lines[1] = lines[1].replace(/^[\x00-\x1F%$>#\s]*/, "")
					}
					// Join lines back
					data = lines.join("\n")
					isFirstChunk = false
				} else {
					data = stripAnsi(data)
				}

				// Ctrl+C detection: if user presses Ctrl+C, treat as command terminated
				if (data.includes("^C") || data.includes("\u0003")) {
					console.log(`[TerminalProcess] 检测到 Ctrl+C 输出，视为命令中断: command="${this.currentCommand}"`)
					if (this.hotTimer) {
						clearTimeout(this.hotTimer)
					}
					this.isHot = false
					break
				}

				// first few chunks could be the command being echoed back, so we must ignore
				// note this means that 'echo' commands won't work
				if (!didOutputNonCommand) {
					const lines = data.split("\n")
					for (let i = 0; i < lines.length; i++) {
						if (command.includes(lines[i].trim())) {
							lines.splice(i, 1)
							i-- // Adjust index after removal
						} else {
							didOutputNonCommand = true
							break
						}
					}
					data = lines.join("\n")
				}

				// 2. Set isHot depending on the command
				// Set to hot to stall API requests until terminal is cool again
				this.isHot = true
				if (this.hotTimer) {
					clearTimeout(this.hotTimer)
				}
				// these markers indicate the command is some kind of local dev server recompiling the app, which we want to wait for output of before sending request to cline
				const compilingMarkers = ["compiling", "building", "bundling", "transpiling", "generating", "starting"]
				const markerNullifiers = [
					"compiled",
					"success",
					"finish",
					"complete",
					"succeed",
					"done",
					"end",
					"stop",
					"exit",
					"terminate",
					"error",
					"fail",
				]
				const isCompiling =
					compilingMarkers.some((marker) => data.toLowerCase().includes(marker.toLowerCase())) &&
					!markerNullifiers.some((nullifier) => data.toLowerCase().includes(nullifier.toLowerCase()))
				this.hotTimer = setTimeout(
					() => {
						this.isHot = false
					},
					isCompiling ? PROCESS_HOT_TIMEOUT_COMPILING : PROCESS_HOT_TIMEOUT_NORMAL,
				)

				// For non-immediately returning commands we want to show loading spinner right away but this wouldn't happen until it emits a line break, so as soon as we get any output we emit "" to let webview know to show spinner
				// This is only done for the sake of unblocking the UI, in case there may be some time before the command emits a full line
				if (!didEmitEmptyLine && !this.fullOutput && data) {
					this.emit("line", "") // empty line to indicate start of command output stream
					didEmitEmptyLine = true
				}

				// Append with cap to avoid OOM: keep only last maxOutputChars characters
				this.fullOutput = (this.fullOutput + data).slice(-this.detectionConfig.maxOutputChars)
				if (this.isListening) {
					this.emitIfEol(data)
					this.lastRetrievedIndex = this.fullOutput.length - this.buffer.length
				}

				// Restart the service detection timer for the next potential pause
				startServiceDetectionTimer()
			}

			this.emitRemainingBufferIfListening()

			// Clear the service detection timer since command completed
			resetServiceDetectionTimer()

			// the command process is finished, let's check the output to see if we need to use the terminal capture fallback
			if (!this.fullOutput.trim()) {
				await returnCurrentTerminalContents()
			}

			// 🔑 关键修复：命令正常退出时也要检测程序类型（但不再在退出时注册服务）
			if (!this.classificationEmitted && this.fullOutput.trim()) {
				console.log(`[TerminalProcess] 命令退出，进行最终检测`)

				const interactiveResult = this.isInteractiveProgram(this.fullOutput)
				if (interactiveResult.isInteractive) {
					console.log(`[TerminalProcess] 命令退出时检测到交互式程序: ${interactiveResult.promptType}`)
					this.classificationEmitted = true
					this.emit("interactive_detected", this.fullOutput, interactiveResult.promptType)
				} else if (this.isServiceProgram(this.fullOutput)) {
					// 过去这里会在命令退出后仍然将其标记为服务并发出 service_detected，导致“幽灵服务”
					// 现在改为仅记录日志，不再在退出路径注册服务，避免后续错误的 Ctrl+C
					console.log(
						`[TerminalProcess] 命令退出时检测到服务型输出，但进程已结束，忽略服务注册: "${this.currentCommand}"`,
					)
					this.classificationEmitted = true
					// 不设置 isServiceRunning，不发出 service_detected
				} else {
					console.log(`[TerminalProcess] 命令退出时未检测到特殊程序类型`)
				}
			}

			// for now we don't want this delaying requests since we don't send diagnostics automatically anymore (previous: "even though the command is finished, we still want to consider it 'hot' in case so that api request stalls to let diagnostics catch up")
			// to explain this further, before we would send workspace diagnostics automatically with each request, but now we only send new diagnostics after file edits, so there's no need to wait for a bit after commands run to let diagnostics catch up

			console.log(`[TerminalProcess] 命令执行完成`)

			// 🔑 只清理定时器，不影响shell integration
			this.cleanupTimers()

			this.emit("completed")
			this.emit("continue")
		} else {
			// no shell integration detected, we'll fallback to running the command and capturing the terminal's output after some time
			console.log(`[TerminalProcess] Shell Integration不可用，使用sendText fallback`)
			terminal.sendText(command, true)

			// wait 3 seconds for the command to run
			await new Promise((resolve) => setTimeout(resolve, 3000))

			// For terminals without shell integration, also try to capture terminal content
			await returnCurrentTerminalContents()
			// For terminals without shell integration, we can't know when the command completes
			// So we'll just emit the continue event after a delay
			this.cleanupTimers() // 🔑 只清理定时器，避免影响后续shell integration
			this.emit("completed")
			this.emit("continue")
			this.emit("no_shell_integration")
			// setTimeout(() => {
			// 	console.log(`Emitting continue after delay for terminal`)
			// 	// can't emit completed since we don't if the command actually completed, it could still be running server
			// }, 500) // Adjust this delay as needed
		}
	}

	// Inspired by https://github.com/sindresorhus/execa/blob/main/lib/transform/split.js
	private emitIfEol(chunk: string) {
		this.buffer += chunk
		let lineEndIndex: number
		while ((lineEndIndex = this.buffer.indexOf("\n")) !== -1) {
			const line = this.buffer.slice(0, lineEndIndex).trimEnd() // removes trailing \r
			// Remove \r if present (for Windows-style line endings)
			// if (line.endsWith("\r")) {
			// 	line = line.slice(0, -1)
			// }
			this.emit("line", line)
			this.buffer = this.buffer.slice(lineEndIndex + 1)
		}
	}

	private emitRemainingBufferIfListening() {
		if (this.buffer && this.isListening) {
			const remainingBuffer = this.removeLastLineArtifacts(this.buffer)
			if (remainingBuffer) {
				this.emit("line", remainingBuffer)
			}
			this.buffer = ""
			this.lastRetrievedIndex = this.fullOutput.length
		}
	}

	continue() {
		this.emitRemainingBufferIfListening()
		this.isListening = false
		this.cleanupTimers() // 🔑 只清理定时器，保持shell integration稳定
		this.emit("continue")
	}

	/**
	 * 只清理定时器，保持其他状态不变
	 */
	private cleanupTimers() {
		// 清理服务检测定时器
		if (this.serviceDetectionTimer) {
			clearTimeout(this.serviceDetectionTimer)
			this.serviceDetectionTimer = null
		}

		// 清理热状态定时器
		if (this.hotTimer) {
			clearTimeout(this.hotTimer)
			this.hotTimer = null
		}
	}

	/**
	 * 安全清理：清理定时器和重置状态标志
	 */
	private safeCleanup() {
		this.cleanupTimers()

		// 重置状态标志
		this.isHot = false
		this.classificationEmitted = false
		this.detectionAttempts = 0
	}

	/**
	 * Send input to the running process
	 * @param input The input text to send
	 * @param addNewLine Whether to add a newline at the end (default: true)
	 */
	sendInput(input: string, addNewLine: boolean = true): void {
		if (this.terminal) {
			this.terminal.sendText(input, addNewLine)
			// Reset the service detection timer since we sent input
			if (this.serviceDetectionTimer) {
				clearTimeout(this.serviceDetectionTimer)
				this.serviceDetectionTimer = null
			}
		}
	}

	getUnretrievedOutput(): string {
		const unretrieved = this.fullOutput.slice(this.lastRetrievedIndex)
		this.lastRetrievedIndex = this.fullOutput.length
		return this.removeLastLineArtifacts(unretrieved)
	}

	/**
	 * 获取当前运行的服务信息
	 */
	getServiceInfo(): { command: string; isRunning: boolean; terminal: vscode.Terminal | null } {
		return {
			command: this.currentCommand,
			isRunning: this.isServiceRunning,
			terminal: this.terminal,
		}
	}

	/**
	 * 检查是否正在运行指定命令的服务
	 */
	isRunningCommand(command: string): boolean {
		return this.isServiceRunning && this.currentCommand === command
	}

	// some processing to remove artifacts like '%' at the end of the buffer (it seems that since vsode uses % at the beginning of newlines in terminal, it makes its way into the stream)
	// This modification will remove '%', '$', '#', or '>' followed by optional whitespace
	removeLastLineArtifacts(output: string) {
		const lines = output.trimEnd().split("\n")
		if (lines.length > 0) {
			const lastLine = lines[lines.length - 1]
			// Remove prompt characters and trailing whitespace from the last line
			lines[lines.length - 1] = lastLine.replace(/[%$#>]\s*$/, "")
		}
		return lines.join("\n").trimEnd()
	}
}

export type TerminalProcessResultPromise = TerminalProcess & Promise<void>

// Similar to execa's ResultPromise, this lets us create a mixin of both a TerminalProcess and a Promise: https://github.com/sindresorhus/execa/blob/main/lib/methods/promise.js
export function mergePromise(process: TerminalProcess, promise: Promise<void>): TerminalProcessResultPromise {
	const nativePromisePrototype = (async () => {})().constructor.prototype
	const descriptors = ["then", "catch", "finally"].map(
		(property) => [property, Reflect.getOwnPropertyDescriptor(nativePromisePrototype, property)] as const,
	)
	for (const [property, descriptor] of descriptors) {
		if (descriptor) {
			const value = descriptor.value.bind(promise)
			Reflect.defineProperty(process, property, { ...descriptor, value })
		}
	}
	return process as TerminalProcessResultPromise
}
