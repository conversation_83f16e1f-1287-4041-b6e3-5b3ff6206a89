{"compilerOptions": {"esModuleInterop": true, "experimentalDecorators": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "lib": ["es2022", "DOM"], "module": "esnext", "moduleResolution": "<PERSON><PERSON><PERSON>", "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "noImplicitReturns": true, "noUnusedLocals": false, "resolveJsonModule": true, "rootDir": ".", "skipLibCheck": true, "sourceMap": true, "strict": true, "target": "es2022", "useDefineForClassFields": true, "useUnknownInCatchVariables": false, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@core/*": ["src/core/*"], "@generated/*": ["src/generated/*"], "@hosts/*": ["src/hosts/*"], "@integrations/*": ["src/integrations/*"], "@packages/*": ["src/packages/*"], "@services/*": ["src/services/*"], "@shared/*": ["src/shared/*"], "@utils/*": ["src/utils/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", ".vscode-test", "webview-ui", "src/test/e2e/**/*"]}